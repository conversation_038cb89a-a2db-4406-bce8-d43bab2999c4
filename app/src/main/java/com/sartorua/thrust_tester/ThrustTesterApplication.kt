package com.sartorua.thrust_tester

import android.app.Application
import android.util.Log

class ThrustTesterApplication : Application() {
    
    companion object {
        private const val TAG = "ThrustTesterApp"
    }
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "Application starting...")
        
        try {
            // Any global initialization can go here
            Log.d(TAG, "Application initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error during application initialization", e)
        }
    }
}
