<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Custom Button Styles -->

    <!-- Primary Button Style -->
    <style name="CustomButton.Primary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_height">@dimen/button_height_standard</item>
        <item name="android:textSize">@dimen/text_size_standard</item>
        <item name="android:textAllCaps">@bool/text_all_caps_disabled</item>
        <item name="cornerRadius">@dimen/corner_radius_standard</item>
        <item name="strokeWidth">@dimen/stroke_width_standard</item>
        <item name="strokeColor">?attr/colorPrimary</item>
        <item name="android:textColor">?attr/colorPrimary</item>
        <item name="backgroundTint">@android:color/transparent</item>
        <item name="rippleColor">?attr/colorPrimaryVariant</item>
    </style>

    <!-- Primary But<PERSON> Filled Style -->
    <style name="CustomButton.Primary.Filled" parent="Widget.Material3.Button">
        <item name="android:layout_height">@dimen/button_height_standard</item>
        <item name="android:textSize">@dimen/text_size_standard</item>
        <item name="android:textAllCaps">@bool/text_all_caps_disabled</item>
        <item name="cornerRadius">@dimen/corner_radius_standard</item>
        <item name="backgroundTint">?attr/colorPrimary</item>
        <item name="android:textColor">?attr/colorOnPrimary</item>
        <item name="rippleColor">?attr/colorPrimaryVariant</item>
    </style>

    <!-- Error Button Style -->
    <style name="CustomButton.Error" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_height">@dimen/button_height_standard</item>
        <item name="android:textSize">@dimen/text_size_standard</item>
        <item name="android:textAllCaps">@bool/text_all_caps_disabled</item>
        <item name="cornerRadius">@dimen/corner_radius_standard</item>
        <item name="strokeWidth">@dimen/stroke_width_standard</item>
        <item name="strokeColor">?attr/colorError</item>
        <item name="android:textColor">?attr/colorError</item>
        <item name="backgroundTint">@android:color/transparent</item>
        <item name="rippleColor">?attr/colorError</item>
    </style>

    <!-- Text Button Style -->
    <style name="CustomButton.Text" parent="Widget.Material3.Button.TextButton">
        <item name="android:layout_height">@dimen/button_height_standard</item>
        <item name="android:textSize">@dimen/text_size_standard</item>
        <item name="android:textAllCaps">@bool/text_all_caps_disabled</item>
        <item name="android:textColor">?attr/colorPrimary</item>
        <item name="rippleColor">?attr/colorPrimaryVariant</item>
    </style>

    <!-- Small Button Style -->
    <style name="CustomButton.Small" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_height">@dimen/button_height_small</item>
        <item name="android:textSize">@dimen/text_size_small</item>
        <item name="android:textAllCaps">@bool/text_all_caps_disabled</item>
        <item name="cornerRadius">@dimen/corner_radius_small</item>
        <item name="strokeWidth">@dimen/stroke_width_small</item>
        <item name="strokeColor">?attr/colorPrimary</item>
        <item name="android:textColor">?attr/colorPrimary</item>
        <item name="backgroundTint">@android:color/transparent</item>
        <item name="rippleColor">?attr/colorPrimaryVariant</item>
        <item name="android:paddingStart">@dimen/padding_horizontal_small</item>
        <item name="android:paddingEnd">@dimen/padding_horizontal_small</item>
    </style>
</resources>
