package com.sartorua.thrust_tester.ui.connect

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.sartorua.thrust_tester.R
import com.sartorua.thrust_tester.databinding.FragmentConnectBinding
import com.sartorua.thrust_tester.usb.ConnectionState
import com.sartorua.thrust_tester.usb.UsbSerialDevice
import kotlinx.coroutines.launch

class ConnectFragment : Fragment() {

    private var _binding: FragmentConnectBinding? = null
    private val binding get() = _binding!!

    private lateinit var connectViewModel: ConnectViewModel
    private lateinit var deviceAdapter: ArrayAdapter<String>
    private lateinit var baudRateAdapter: ArrayAdapter<Int>

    private var deviceList: List<UsbSerialDevice> = emptyList()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        connectViewModel = ViewModelProvider(this)[ConnectViewModel::class.java]
        _binding = FragmentConnectBinding.inflate(inflater, container, false)

        setupUI()
        observeViewModel()

        return binding.root
    }

    private fun setupUI() {
        // Setup device spinner
        deviceAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, mutableListOf<String>())
        deviceAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerDevices.adapter = deviceAdapter

        // Setup baud rate spinner
        baudRateAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, connectViewModel.baudRateOptions)
        baudRateAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerBaudRate.adapter = baudRateAdapter

        // Set default baud rate to 9600
        binding.spinnerBaudRate.setSelection(0)

        // Setup click listeners
        binding.buttonRefreshDevices.setOnClickListener {
            connectViewModel.refreshDevices()
        }

        binding.buttonConnect.setOnClickListener {
            val selectedPosition = binding.spinnerDevices.selectedItemPosition
            if (selectedPosition >= 0 && selectedPosition < deviceList.size) {
                val selectedDevice = deviceList[selectedPosition]
                val selectedBaudRate = binding.spinnerBaudRate.selectedItem as Int

                connectViewModel.selectDevice(selectedDevice)
                connectViewModel.setBaudRate(selectedBaudRate)
                connectViewModel.connect()
            } else {
                Toast.makeText(requireContext(), "Please select a device", Toast.LENGTH_SHORT).show()
            }
        }

        binding.buttonDisconnect.setOnClickListener {
            connectViewModel.disconnect()
        }

        binding.buttonClearData.setOnClickListener {
            connectViewModel.clearReceivedData()
        }

        binding.buttonSendData.setOnClickListener {
            val data = binding.editSendData.text.toString()
            if (data.isNotEmpty()) {
                connectViewModel.sendData(data + "\n") // Add newline
                binding.editSendData.text?.clear()
            }
        }
    }

    private fun observeViewModel() {
        // Observe connection state
        viewLifecycleOwner.lifecycleScope.launch {
            connectViewModel.connectionState.collect { state ->
                updateConnectionUI(state)
            }
        }

        // Observe available devices
        viewLifecycleOwner.lifecycleScope.launch {
            connectViewModel.availableDevices.collect { devices ->
                updateDeviceList(devices)
            }
        }

        // Observe received data
        viewLifecycleOwner.lifecycleScope.launch {
            connectViewModel.receivedData.collect { data ->
                binding.textReceivedData.text = if (data.isEmpty()) "No data received..." else data
                // Auto-scroll to bottom
                binding.textReceivedData.post {
                    val scrollView = binding.textReceivedData.parent as? View
                    scrollView?.let { (it.parent as? View)?.scrollTo(0, it.bottom) }
                }
            }
        }

        // Observe error messages
        viewLifecycleOwner.lifecycleScope.launch {
            connectViewModel.errorMessage.collect { error ->
                error?.let {
                    Toast.makeText(requireContext(), it, Toast.LENGTH_LONG).show()
                    connectViewModel.clearError()
                }
            }
        }
    }

    private fun updateConnectionUI(state: ConnectionState) {
        when (state) {
            ConnectionState.DISCONNECTED -> {
                binding.textConnectionStatus.text = "Disconnected"
                binding.textConnectionStatus.setTextColor(ContextCompat.getColor(requireContext(), android.R.color.holo_red_dark))
                binding.buttonConnect.isEnabled = true
                binding.buttonDisconnect.isEnabled = false
                binding.buttonSendData.isEnabled = false
                binding.spinnerDevices.isEnabled = true
                binding.spinnerBaudRate.isEnabled = true
            }
            ConnectionState.CONNECTING -> {
                binding.textConnectionStatus.text = "Connecting..."
                binding.textConnectionStatus.setTextColor(ContextCompat.getColor(requireContext(), android.R.color.holo_orange_dark))
                binding.buttonConnect.isEnabled = false
                binding.buttonDisconnect.isEnabled = false
                binding.buttonSendData.isEnabled = false
                binding.spinnerDevices.isEnabled = false
                binding.spinnerBaudRate.isEnabled = false
            }
            ConnectionState.CONNECTED -> {
                binding.textConnectionStatus.text = "Connected"
                binding.textConnectionStatus.setTextColor(ContextCompat.getColor(requireContext(), android.R.color.holo_green_dark))
                binding.buttonConnect.isEnabled = false
                binding.buttonDisconnect.isEnabled = true
                binding.buttonSendData.isEnabled = true
                binding.spinnerDevices.isEnabled = false
                binding.spinnerBaudRate.isEnabled = false
            }
        }
    }

    private fun updateDeviceList(devices: List<UsbSerialDevice>) {
        deviceList = devices
        val deviceNames = devices.map { "${it.name} (VID:${it.vendorId.toString(16)}, PID:${it.productId.toString(16)})" }

        deviceAdapter.clear()
        if (deviceNames.isEmpty()) {
            deviceAdapter.add("No USB serial devices found")
            binding.buttonConnect.isEnabled = false
        } else {
            deviceAdapter.addAll(deviceNames)
            binding.buttonConnect.isEnabled = true
        }
        deviceAdapter.notifyDataSetChanged()
    }

    override fun onResume() {
        super.onResume()
        connectViewModel.refreshDevices()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
