# Connection UI Testing Guide

## Test Cases for Single Button Implementation

### 1. Initial State (Disconnected)
- [ ] <PERSON><PERSON> displays "Connect"
- [ ] But<PERSON> is enabled when USB devices are available
- [ ] <PERSON><PERSON> is disabled when no USB devices are found
- [ ] Device and baud rate spinners are enabled

### 2. Connecting State
- [ ] <PERSON><PERSON> displays "Connecting..."
- [ ] <PERSON><PERSON> is disabled during connection attempt
- [ ] Device and baud rate spinners are disabled
- [ ] Send data button remains disabled

### 3. Connected State
- [ ] <PERSON><PERSON> displays "Disconnect"
- [ ] But<PERSON> is enabled
- [ ] Send data button becomes enabled
- [ ] Device and baud rate spinners remain disabled

### 4. Button Actions
- [ ] Clicking "Connect" initiates connection with selected device and baud rate
- [ ] Clicking "Disconnect" terminates the connection
- [ ] <PERSON><PERSON> shows appropriate error messages if connection fails
- [ ] <PERSON><PERSON> returns to "Connect" state after disconnection

### 5. Edge Cases
- [ ] No devices available: <PERSON><PERSON> shows "Connect" but is disabled
- [ ] Device selection validation: Shows toast if no device selected
- [ ] Connection errors: <PERSON><PERSON> returns to "Connect" state with error message

## How to Test
1. Build and install the app on an Android device
2. Navigate to the Connect tab
3. Test each state transition by connecting/disconnecting USB devices
4. Verify button text and enabled state changes correctly
5. Test error scenarios (no devices, connection failures)
